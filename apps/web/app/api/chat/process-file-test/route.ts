import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { testType } = await request.json();
    
    console.log(`Testing file processing: ${testType}`);
    
    switch (testType) {
      case 'text':
        return NextResponse.json({
          success: true,
          result: await testTextProcessing(),
          message: "Text processing test completed"
        });
        
      case 'pdf':
        return NextResponse.json({
          success: true,
          result: await testPDFProcessing(),
          message: "PDF processing test completed"
        });
        
      case 'excel':
        return NextResponse.json({
          success: true,
          result: await testExcelProcessing(),
          message: "Excel processing test completed"
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: "Unknown test type"
        }, { status: 400 });
    }
  } catch (error: any) {
    console.error('File processing test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

async function testTextProcessing() {
  try {
    // Test text file processing
    const testContent = "This is a test text file content.\nIt has multiple lines.\nAnd should be processed correctly.";
    
    return {
      type: 'text',
      content: testContent,
      length: testContent.length,
      lines: testContent.split('\n').length
    };
  } catch (error: any) {
    throw new Error(`Text processing failed: ${error.message}`);
  }
}

async function testPDFProcessing() {
  try {
    // Test PDF parsing library
    const pdfParse = (await import('pdf-parse')).default;
    
    // Create a simple test buffer (this will fail but we can see if the import works)
    const testBuffer = Buffer.from("This is not a real PDF");
    
    try {
      const result = await pdfParse(testBuffer);
      return {
        type: 'pdf',
        success: true,
        result: result
      };
    } catch (pdfError: any) {
      // Expected to fail with test buffer, but import should work
      return {
        type: 'pdf',
        importSuccess: true,
        expectedError: pdfError.message,
        message: "PDF library imported successfully, error expected with test buffer"
      };
    }
  } catch (error: any) {
    throw new Error(`PDF processing failed: ${error.message}`);
  }
}

async function testExcelProcessing() {
  try {
    // Test XLSX library
    const XLSX = await import('xlsx');
    
    // Create a simple test workbook
    const testData = [
      ['Name', 'Age', 'City'],
      ['John', 30, 'New York'],
      ['Jane', 25, 'Los Angeles']
    ];
    
    const worksheet = XLSX.utils.aoa_to_sheet(testData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Test Sheet');
    
    // Convert back to test parsing
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    const parsedWorkbook = XLSX.read(buffer, { type: 'buffer' });
    const parsedData = XLSX.utils.sheet_to_json(parsedWorkbook.Sheets['Test Sheet'], { header: 1 });
    
    return {
      type: 'excel',
      success: true,
      originalData: testData,
      parsedData: parsedData,
      sheets: parsedWorkbook.SheetNames
    };
  } catch (error: any) {
    throw new Error(`Excel processing failed: ${error.message}`);
  }
}

export async function GET() {
  return NextResponse.json({
    message: "File processing test endpoint",
    availableTests: ['text', 'pdf', 'excel'],
    usage: "POST with { testType: 'text' | 'pdf' | 'excel' }"
  });
}
