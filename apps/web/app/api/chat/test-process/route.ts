import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Test the pdf-parse import
    const pdfParse = await import('pdf-parse');
    console.log('PDF Parse import successful:', typeof pdfParse.default);
    
    // Test the XLSX import
    const XLSX = await import('xlsx');
    console.log('XLSX import successful:', typeof XLSX);
    
    return NextResponse.json({
      success: true,
      pdfParseType: typeof pdfParse.default,
      xlsxType: typeof XLSX,
      message: "All imports working correctly"
    });
  } catch (error: any) {
    console.error('Import test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    // Test a simple text processing
    const testText = "This is a test text file content.";
    
    // Test PDF parsing with a simple buffer (this will fail but we can see the error)
    const pdfParse = (await import('pdf-parse')).default;
    console.log('PDF Parse function:', typeof pdfParse);
    
    // Test XLSX
    const XLSX = await import('xlsx');
    console.log('XLSX object:', Object.keys(XLSX));
    
    return NextResponse.json({
      success: true,
      testText,
      pdfParseType: typeof pdfParse,
      xlsxKeys: Object.keys(XLSX).slice(0, 10), // First 10 keys
      message: "Processing test successful"
    });
  } catch (error: any) {
    console.error('Processing test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
