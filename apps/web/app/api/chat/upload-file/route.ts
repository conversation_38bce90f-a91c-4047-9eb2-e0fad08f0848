import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import { 
  ChatSupportedFileTypes, 
  MimeTypeMap, 
  getFileTypeFromMimeType 
} from "@/lib/constant/supported-extensions";

const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

// File size limits by type (in bytes)
const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024, // 10MB
  audio: 50 * 1024 * 1024, // 50MB
  document: 25 * 1024 * 1024, // 25MB
  text: 5 * 1024 * 1024, // 5MB
  spreadsheet: 15 * 1024 * 1024, // 15MB
};

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const tenantId = formData.get("tenantId") as string;

    if (!file || !tenantId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get file type category from MIME type
    const fileTypeCategory = getFileTypeFromMimeType(file.type);
    if (!fileTypeCategory) {
      return NextResponse.json(
        { error: `Unsupported file type: ${file.type}. Supported types: images, audio, documents, text files, and spreadsheets.` },
        { status: 400 }
      );
    }

    // Validate file size based on category
    const maxSize = FILE_SIZE_LIMITS[fileTypeCategory];
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File size too large. Maximum size for ${fileTypeCategory} files is ${Math.round(maxSize / (1024 * 1024))}MB.` },
        { status: 400 }
      );
    }

    // Get file extension
    const fileExtension = MimeTypeMap[file.type as keyof typeof MimeTypeMap] || 
                         file.name.split('.').pop()?.toLowerCase() || 'bin';

    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path for chat files
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const blobName = `chat-files/${tenantId}/${fileTypeCategory}/${timestamp}-${randomId}.${fileExtension}`;
    
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Upload file with appropriate metadata
    await blockBlobClient.uploadData(arrayBuffer, {
      blobHTTPHeaders: { 
        blobContentType: file.type,
        blobCacheControl: "public, max-age=31536000", // Cache for 1 year
      },
      metadata: {
        originalName: file.name,
        fileCategory: fileTypeCategory,
        uploadedBy: session.user.email,
        tenantId: tenantId,
      }
    });

    // Generate unique ID for the file
    const fileId = `file_${timestamp}_${randomId}`;

    // Prepare metadata based on file type
    const metadata: any = {};
    
    if (fileTypeCategory === 'image') {
      // For images, we might want to extract dimensions later
      metadata.dimensions = { width: 0, height: 0 }; // Placeholder
    } else if (fileTypeCategory === 'audio') {
      // For audio, we might want to extract duration later
      metadata.duration = 0; // Placeholder
    } else if (fileTypeCategory === 'document') {
      // For documents, we might want to extract page count later
      metadata.pages = 0; // Placeholder
    }

    const response = {
      id: fileId,
      url: blockBlobClient.url,
      name: file.name,
      type: file.type,
      size: file.size,
      fileType: fileTypeCategory,
      processingStatus: 'pending' as const,
      metadata,
      message: "File uploaded successfully",
    };

    // For certain file types, trigger background processing
    if (fileTypeCategory === 'audio' || fileTypeCategory === 'document' || fileTypeCategory === 'spreadsheet') {
      // TODO: Trigger background processing for transcription, OCR, or parsing
      // This would be implemented as a separate service call
      console.log(`File ${fileId} queued for ${fileTypeCategory} processing`);
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}
