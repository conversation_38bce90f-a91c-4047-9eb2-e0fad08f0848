"use client";

import React, { useState } from "react";
import { FileUpload } from "@/components/wrapper-screens/chat/components/FileUpload";
import { FilePreview } from "@/components/wrapper-screens/chat/components/FilePreview";
import { FileAttachment } from "@/components/wrapper-screens/chat/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function FileUploadTestPage() {
  const [selectedFiles, setSelectedFiles] = useState<FileAttachment[]>([]);

  const handleFilesSelected = (files: FileAttachment[]) => {
    setSelectedFiles(files);
  };

  const handleRemoveFile = (fileId: string) => {
    setSelectedFiles(selectedFiles.filter((file) => file.id !== fileId));
  };

  const clearAllFiles = () => {
    setSelectedFiles([]);
  };

  const getFileTypeStats = () => {
    const stats = selectedFiles.reduce((acc, file) => {
      acc[file.fileType] = (acc[file.fileType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return stats;
  };

  const getTotalSize = () => {
    return selectedFiles.reduce((total, file) => total + file.size, 0);
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const fileTypeStats = getFileTypeStats();

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">File Upload Test</h1>
          <p className="text-muted-foreground">
            Test the new multi-format file upload system with support for images, audio, documents, text files, and spreadsheets.
          </p>
        </div>

        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Files</CardTitle>
            <CardDescription>
              Supported formats: Images (JPG, PNG, WebP, GIF, BMP), Audio (MP3, WAV, M4A, OGG, FLAC), 
              Documents (PDF, DOCX, PPTX), Text (TXT, MD), Spreadsheets (CSV, XLSX, XLS)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FileUpload
              onFilesSelected={handleFilesSelected}
              selectedFiles={selectedFiles}
              onRemoveFile={handleRemoveFile}
              maxFiles={10}
            />
          </CardContent>
        </Card>

        {/* Stats Section */}
        {selectedFiles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Upload Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{selectedFiles.length}</div>
                  <div className="text-sm text-muted-foreground">Total Files</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{formatBytes(getTotalSize())}</div>
                  <div className="text-sm text-muted-foreground">Total Size</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {selectedFiles.filter(f => f.processingStatus === 'completed').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Processed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {selectedFiles.filter(f => f.processingStatus === 'processing').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Processing</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">File Types:</h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(fileTypeStats).map(([type, count]) => (
                    <Badge key={type} variant="secondary">
                      {type}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="mt-4">
                <Button onClick={clearAllFiles} variant="outline" size="sm">
                  Clear All Files
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Preview Section */}
        {selectedFiles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>File Previews</CardTitle>
              <CardDescription>
                Preview and interact with your uploaded files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FilePreview files={selectedFiles} />
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Click the "+" button or drag and drop files to upload</li>
              <li>Try different file types: images, PDFs, text files, CSV/Excel files, audio files</li>
              <li>Watch the processing status for files that require conversion</li>
              <li>View the file previews and processed content</li>
              <li>Test the file removal functionality</li>
              <li>Check the upload statistics and file type breakdown</li>
            </ol>
            
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Processing Features:</h4>
              <ul className="text-sm space-y-1">
                <li>• <strong>PDFs:</strong> Text extraction with OCR fallback for image-based PDFs</li>
                <li>• <strong>Audio:</strong> Transcription placeholder (ready for Whisper/Azure Speech integration)</li>
                <li>• <strong>Spreadsheets:</strong> CSV/Excel parsing with table formatting</li>
                <li>• <strong>Text files:</strong> Direct content reading</li>
                <li>• <strong>Images:</strong> Vision model analysis for descriptions</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
