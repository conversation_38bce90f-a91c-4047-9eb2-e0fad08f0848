import { redirect } from "next/navigation";
import { SidebarInset, SidebarProvider } from "@/components/layouts/sidebar";
import { AppSidebar } from "@/components/layouts/app-sidebar";
import { SiteHeader } from "@/components/layouts/site-header";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { getChat, getWorkspace } from "@/services";
import { cookies } from "next/headers";
import { MemberAppSidebar } from "@/components/layouts/member-sidebar";
import { getChatGroup } from "@/services/src/chat-group";
import { QuickAskWrapper } from "@/components/quick-ask";
import { GlobalSearchProvider } from "@/components/global-search";

export default async function HomePage({ children }: any) {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect("/sign-in");
  } else if (!session?.memberships || session?.memberships?.length === 0) {
    return redirect("/onboarding");
  }
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;

  const memberships = session?.memberships || [];
  const userRole = memberships.find((m) => m.tenant.id === tenantId)?.role;
  const isAdmin = ["admin", "owner", "custom"].includes(
    userRole?.toLowerCase()
  );
  let workspace, chatHistory, group;
  if (isAdmin) workspace = await getWorkspace(tenantId, session?.userId);
  if (!isAdmin) {
    group = (await getChatGroup({ userId: session?.userId, tenantId })) ?? [];
    chatHistory = (await getChat({ userId: session?.userId, tenantId })) ?? [];
  }
  return (
    <GlobalSearchProvider tenantId={tenantId}>
      <QuickAskWrapper>
        <div className="[--header-height:calc(theme(spacing.14))] min-h-screen flex flex-col">
          <SidebarProvider className="flex flex-col flex-1">
            <SiteHeader />
            <div className="flex flex-1 overflow-hidden">
              {isAdmin ? (
                <AppSidebar
                  workspace={workspace?.workspaces ?? []}
                  session={session}
                  role={userRole?.toLowerCase()}
                />
              ) : (
                <MemberAppSidebar
                  session={session}
                  group={group ?? []}
                  tenantId={tenantId}
                  chatHistory={
                    chatHistory?.chat?.map((item) => ({
                      id: item?.id ?? "",
                      name: item?.title ?? "",
                      url: `/ask-ai/${item?.id}/`,
                    })) ?? []
                  }
                />
              )}
              <SidebarInset className="p-4 overflow-auto"> {children}</SidebarInset>
            </div>
          </SidebarProvider>
        </div>
      </QuickAskWrapper>
    </GlobalSearchProvider>
  );
}
