import ProjectCard from "@/components/cards/home/<USER>";
import { getWorkspace } from "@/services";
import { cookies } from "next/headers";
import { getTranslations } from "@/lib/server-i18n";
import CreateWorkspaceButton from "@/components/workspace/create-workspace-button";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  const [workspace] = await Promise.all([getWorkspace(tenantId, userId)]);
  const { t } = await getTranslations();

  return (
    <div className="h-full px-1 py-2 md:px-4">
      <div className="flex items-center justify-between mb-6">
        <h4 className="text-2xl font-bold md:text-3xl tracking-tight">
          {t("sidebar.myHub")}
        </h4>
        <CreateWorkspaceButton />
      </div>

      {(workspace?.workspaces ?? []).length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="rounded-full bg-primary/10 p-6 mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-primary"
            >
              <path d="M3 3h18v18H3z" />
              <path d="M12 8v8" />
              <path d="M8 12h8" />
            </svg>
          </div>
          <h2 className="text-2xl font-semibold mb-2">
            {t("workspace.noWorkspacesYet")}
          </h2>
          <p className="text-muted-foreground mb-6 text-center max-w-md">
            {t("workspace.createFirstWorkspace")}
          </p>
          <CreateWorkspaceButton size="lg" />
        </div>
      ) : (
        <div className="w-full max-w-[1800px] mx-auto">
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 3xl:grid-cols-7 4xl:grid-cols-8 5xl:grid-cols-10">
            {(workspace?.workspaces ?? []).map((project) => (
              <div key={project?.id} className="min-w-0">
                <ProjectCard
                  workspace={project}
                  initials={project?.initials}
                  title={project?.name}
                  description={project?.description}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
