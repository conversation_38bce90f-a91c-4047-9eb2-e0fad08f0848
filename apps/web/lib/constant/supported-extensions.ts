export const SupportedExtensions = [
  "pdf",
  "txt",
  "csv",
  "md",
  "docx",
  "xlsx",
  "xls",
  "pptx",
  "markdown",
  "jpg",
  "jpeg",
  "png",
  "gif",
  "bmp",
  "webp",
  "mp3",
  "wav",
  "m4a",
  "ogg",
  "flac",
];

// File type mappings for chat uploads
export const ChatSupportedFileTypes = {
  image: ["jpg", "jpeg", "png", "webp", "gif", "bmp"],
  audio: ["mp3", "wav", "m4a", "ogg", "flac"],
  document: ["pdf", "docx", "pptx"],
  text: ["txt", "md", "markdown"],
  spreadsheet: ["csv", "xlsx", "xls"],
} as const;

// MIME type mappings
export const MimeTypeMap = {
  // Images
  "image/jpeg": "jpg",
  "image/jpg": "jpg",
  "image/png": "png",
  "image/webp": "webp",
  "image/gif": "gif",
  "image/bmp": "bmp",

  // Audio
  "audio/mpeg": "mp3",
  "audio/mp3": "mp3",
  "audio/wav": "wav",
  "audio/wave": "wav",
  "audio/x-wav": "wav",
  "audio/mp4": "m4a",
  "audio/x-m4a": "m4a",
  "audio/ogg": "ogg",
  "audio/flac": "flac",

  // Documents
  "application/pdf": "pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",

  // Text
  "text/plain": "txt",
  "text/markdown": "md",

  // Spreadsheets
  "text/csv": "csv",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "application/vnd.ms-excel": "xls",
} as const;

// Get file type category from extension
export const getFileTypeFromExtension = (extension: string): keyof typeof ChatSupportedFileTypes | null => {
  const ext = extension.toLowerCase();
  for (const [category, extensions] of Object.entries(ChatSupportedFileTypes)) {
    if (extensions.includes(ext as any)) {
      return category as keyof typeof ChatSupportedFileTypes;
    }
  }
  return null;
};

// Get file type category from MIME type
export const getFileTypeFromMimeType = (mimeType: string): keyof typeof ChatSupportedFileTypes | null => {
  const extension = MimeTypeMap[mimeType as keyof typeof MimeTypeMap];
  if (extension) {
    return getFileTypeFromExtension(extension);
  }
  return null;
};
