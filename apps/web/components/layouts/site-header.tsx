"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useSidebar } from "@/components/layouts/sidebar";
import { ModeToggle } from "../theme/mode-toggle";
import { useTheme } from "next-themes";
import { useLanguage } from "@/lib/language-context";
import { generateBreadcrumbs } from "@/utils/breadcrumbs";
import { fetchWorkspaceBySlugWithCache } from "@/utils/workspace";
import { QuickAskButton } from "@/components/quick-ask";
import { NotificationBell } from "@/components/wrapper-screens/chat/components/NotificationBell";
import { GlobalSearchButton } from "@/components/global-search";

export function SiteHeader() {
  const { resolvedTheme } = useTheme();
  const { toggleSidebar } = useSidebar();
  const pathname = usePathname() || "";
  const { t } = useLanguage();
  const [breadcrumbs, setBreadcrumbs] = useState<
    Array<{ label: string; href: string; active: boolean }>
  >([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadBreadcrumbs = async () => {
      setIsLoading(true);
      try {
        // Extract workspace slug from pathname if present
        const segments = pathname.split("/").filter(Boolean);
        let workspaceData = null;

        // If we're in a workspace route, fetch the workspace data
        if (segments.length >= 2 && segments[0] === "workspace") {
          const workspaceSlug = segments[1];
          workspaceData = await fetchWorkspaceBySlugWithCache(workspaceSlug);
        }

        // Generate breadcrumbs with the workspace data
        const newBreadcrumbs = await generateBreadcrumbs(
          pathname,
          t,
          workspaceData
        );
        setBreadcrumbs(newBreadcrumbs);
      } catch (error) {
        console.error("Error loading breadcrumbs:", error);
        // Fallback to empty breadcrumbs
        setBreadcrumbs([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadBreadcrumbs();
  }, [pathname, t]);

  return (
    <>
      <header className="fixed top-0 left-0 w-full z-50 items-center border-b bg-sidebar/95 backdrop-blur supports-[backdrop-filter]:bg-sidebar/60">
        <div className="flex h-[--header-height] w-full items-center gap-2 px-4">
          <Button
            className="h-8 w-8"
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
          >
            <Image
              src={`/logo-${resolvedTheme}.png`}
              alt={t("workspace.appName")}
              width={18}
              height={18}
              className="h-full w-full"
            />
          </Button>
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="hidden sm:block">
            <BreadcrumbList>
              {isLoading ? (
                <BreadcrumbItem>
                  <BreadcrumbPage>Loading...</BreadcrumbPage>
                </BreadcrumbItem>
              ) : (
                breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem>
                      {breadcrumb.active ? (
                        <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink asChild>
                          <Link href={breadcrumb.href}>{breadcrumb.label}</Link>
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                  </React.Fragment>
                ))
              )}
            </BreadcrumbList>
          </Breadcrumb>
          <div className="flex items-center gap-2  ml-auto w-auto">
            <QuickAskButton />
            <GlobalSearchButton size="sm" />
            <NotificationBell />
            <ModeToggle />
          </div>
        </div>
      </header>
      {/* Spacer to prevent content from being hidden behind the fixed navbar */}
      <div className="h-[--header-height] w-full" />
    </>
  );
}
