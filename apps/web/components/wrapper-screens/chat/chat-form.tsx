"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useChat } from "@ai-sdk/react";
import { MessageLoader } from "@/components/ui/message-loader";
import { CitationModal } from "./citation-modal";
import { useSidebar } from "@/components/layouts/sidebar";
import { useLanguage } from "@/lib/language-context";
import { useIsMobile } from "@/hooks/use-mobile";
import { addAuthHeaders } from "@/lib/api/auth-token";
import { createChat } from "@/services/src/chat";
import { createMessage } from "@/services/src/message";
import { workspaceChatService } from "@/services/workspace-chat";
import { performWebSearch } from "@/services/src/web-search";
import { ChatMessage } from "./components/ChatMessage";
import { ChatInputForm } from "./components/ChatInputForm";
import { ChatHeader } from "./components/ChatHeader";
import { useCarouselState } from "./hooks/useCarouselState";
import { useMessageRegeneration } from "./hooks/useMessageRegeneration";
import { useMessageFeedback } from "./hooks/useMessageFeedback";
import { processMessages } from "./utils/message-utils";
import { ChatFormProps, Message, Source, ImageAttachment, FileAttachment } from "./types";
import toast from "react-hot-toast";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

export function ChatForm({ userId, chats, tenantId, userName }: ChatFormProps) {
  const router = useRouter();
  const { state } = useSidebar(); // Get sidebar state
  const { t } = useLanguage();
  const isMobile = useIsMobile(); // Check if device is mobile
  const [chatId, setChatId] = useState<string | null>(chats?.id ?? null);
  const [chatTitle, setChatTitle] = useState<string>(chats?.title || "");
  const [input, setInput] = useState("");
  const [headers, setHeaders] = useState({});
  const [messages, setMessages] = useState<Message[]>(chats?.messages ?? []);
  const [selectedSource, setSelectedSource] = useState<Source | null>(null);
  const [isCitationModalOpen, setIsCitationModalOpen] = useState(false);

  // Web search state
  const [includeWebResults, setIncludeWebResults] = useState(false);
  const [webSearchLimitExceeded, setWebSearchLimitExceeded] = useState(false);

  // Image upload state (legacy)
  const [selectedImages, setSelectedImages] = useState<ImageAttachment[]>(
    chats?.messages?.[0]?.metadata?.images ?? []
  );

  // File upload state (new system)
  const [selectedFiles, setSelectedFiles] = useState<FileAttachment[]>(
    chats?.messages?.[0]?.metadata?.files ?? []
  );

  // Feature flag for new file upload system
  const [useNewFileUpload] = useState(true); // Set to true to enable new file upload

  // Reference to the message container for auto-scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Save messages to local storage when they change
  useEffect(() => {
    if (chatId && messages.length > 0) {
      try {
        localStorage.setItem(`chat_${chatId}`, JSON.stringify(messages));
      } catch (error) {
        console.error("Error saving messages to local storage:", error);
      }
    }
  }, [messages, chatId]);

  // Load messages from local storage on initial load if not provided from server
  useEffect(() => {
    if (chatId && (!chats?.messages || chats.messages.length === 0)) {
      try {
        const savedMessages = localStorage.getItem(`chat_${chatId}`);
        if (savedMessages) {
          setMessages(JSON.parse(savedMessages));
        }
      } catch (error) {
        console.error("Error loading messages from local storage:", error);
      }
    }
  }, [chatId, chats?.messages]);

  // Process messages from database to extract images from metadata
  useEffect(() => {
    if (chats?.messages && chats.messages.length > 0) {
      const processedMessages = chats.messages.map((message: any) => ({
        ...message,
        images: message.metadata?.images || message.images || undefined,
      }));
      setMessages(processedMessages);
    }
  }, [chats?.messages]);

  // Process the messages to group regenerated messages with their originals
  const processedMessages = processMessages(messages);

  // Use our custom hooks
  const { displayIndicesRef, updateDisplayIndex } =
    useCarouselState(processedMessages);

  const { handleRegenerate, isLoading } = useMessageRegeneration(
    messages,
    setMessages,
    chatId,
    userId,
    tenantId,
    userName,
    headers,
    updateDisplayIndex
  );

  const { handleFeedback } = useMessageFeedback(
    processedMessages,
    setMessages,
    chatId,
    displayIndicesRef
  );

  // Function to toggle citation accordion
  const toggleCitationAccordion = (_messageId: string) => {
    // This is now handled within the MessageCitations component
    // No need to track expanded state at this level
  };

  // Handle title update
  const handleTitleUpdate = (newTitle: string) => {
    setChatTitle(newTitle);
  };

  // Handle chat deletion
  const handleChatDelete = () => {
    // Chat will be deleted and user redirected by the ChatHeader component
    // No additional action needed here
  };

  // Set up authentication headers
  useEffect(() => {
    getHeaders();
  }, []);

  const getHeaders = async () => {
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });
    setHeaders(headers);
  };

  const {
    handleInputChange,
    handleSubmit: handleAISubmit,
    status,
  } = useChat({
    api: `${API_BASE_URL}${API_VERSION}/workspace-chat/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${userName}`,
    body: {
      stream: true,
      config: {
        includeWebResults: includeWebResults === true,
      },
      messages: [
        ...messages.map((msg) => ({
          id: msg.id || Math.random().toString(),
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
        })),
        {
          id: Math.random().toString(),
          role: "user",
          content: input,
          createdAt: new Date(),
        },
      ],
      images: selectedImages.length > 0 ? selectedImages : undefined,
    },
    headers,
    onResponse: async (response) => {
      if (!response.ok) {
        console.error("API response error:", response.statusText);
        return;
      }
      setInput("");

      // Add empty assistant message that will be updated with streamed content
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: "assistant_tempid",
          role: "assistant",
          content: "",
          metadata: {},
          sources: [],
        },
      ]);

      // Save user message to database immediately
      if (chatId) {
        try {
          const userMessage = await createMessage({
            chatId: chatId,
            userId,
            content: input,
            role: "user",
            metadata: {
              includeWebResults: includeWebResults === true,
              hasImages: selectedImages.length > 0,
              images: selectedImages.length > 0 ? selectedImages : undefined,
            },
          });

          // Update the user message in the UI with its ID
          if (userMessage && !userMessage.error) {
            setMessages((prevMessages) => {
              const updatedMessages = [...prevMessages];
              // Find the last user message and update it with the ID
              for (let i = updatedMessages.length - 2; i >= 0; i--) {
                if (updatedMessages[i].role === "user") {
                  updatedMessages[i] = {
                    ...updatedMessages[i],
                    id: userMessage.id,
                  };
                  break;
                }
              }
              return updatedMessages;
            });
          }
        } catch (error) {
          console.error("Error saving user message:", error);
        }
      }

      // Set up streaming response handling
      const reader = response.body?.getReader();
      let assistantMessage = "";
      let sources: Source[] = [];

      // Try to parse sources from the first chunk
      const parseSourcesFromResponse = async (
        reader: ReadableStreamDefaultReader<Uint8Array>
      ) => {
        try {
          const { value } = await reader.read();
          const firstChunk = new TextDecoder().decode(value);
          console.log({ firstChunk });

          try {
            // Try to parse the first chunk as JSON to extract sources
            const parsedChunk = JSON.parse(firstChunk);

            // Check if this is the first chunk with the marker
            if (parsedChunk.is_first_chunk && parsedChunk.sources) {
              sources = parsedChunk.sources;
              // Return the answer part for further processing
              return {
                initialContent: parsedChunk.answer || "",
                sources,
                reader,
              };
            }

            // Check for sources in the standard format as a fallback
            if (parsedChunk.sources) {
              sources = parsedChunk.sources;
              // Return only the answer part for further processing
              return {
                initialContent: parsedChunk.answer || "",
                sources,
                reader,
              };
            }
          } catch (e) {
            // If not JSON, log the error and use the chunk as plain text
            console.error("First chunk is not valid JSON:", e);
            console.log("First chunk content:", firstChunk);
            console.log("Using first chunk as plain text");
          }

          return {
            initialContent: firstChunk,
            sources: [],
            reader,
          };
        } catch (e) {
          console.error("Error parsing sources:", e);
          return {
            initialContent: "",
            sources: [],
            reader,
          };
        }
      };

      if (reader) {
        try {
          // Try to parse sources from the first chunk
          const {
            initialContent,
            sources: parsedSources,
            reader: updatedReader,
          } = await parseSourcesFromResponse(reader);
          assistantMessage = initialContent;
          sources = parsedSources;

          // Update the message with initial content and sources
          setMessages((prevMessages) => {
            const updatedMessages = [...prevMessages];
            if (updatedMessages.length > 0) {
              updatedMessages[updatedMessages.length - 1] = {
                ...updatedMessages[updatedMessages.length - 1],
                role: "assistant",
                content: initialContent,
                sources: sources,
              };
            }
            return updatedMessages;
          });

          // Continue reading the stream
          // eslint-disable-next-line no-constant-condition
          while (true) {
            const { done, value } = await updatedReader.read();
            if (done) break;

            // Convert the chunk to text and accumulate
            const chunk = new TextDecoder().decode(value);

            // Try to parse the chunk as JSON to extract sources
            try {
              const parsedChunk = JSON.parse(chunk);
              if (parsedChunk.sources) {
                // If this is a sources chunk, store the sources but don't add to message content
                sources = parsedChunk.sources;
                // Skip adding this chunk to the message content
                continue;
              }
            } catch (e) {
              // Not JSON, treat as normal text
            }

            assistantMessage += chunk;

            // Clean up the message content
            let cleanedMessage = assistantMessage;
            if (cleanedMessage.startsWith("Answer:")) {
              cleanedMessage = cleanedMessage.replace("Answer:", "").trim();
            }

            // Update the messages state with the cleaned content
            setMessages((prevMessages) => {
              const updatedMessages = [...prevMessages];
              if (updatedMessages.length > 0) {
                if (cleanedMessage.startsWith("ERROR:")) {
                  const errorMessage = cleanedMessage
                    .replace("ERROR:", "")
                    .trim();
                  updatedMessages[updatedMessages.length - 1] = {
                    ...updatedMessages[updatedMessages.length - 1],
                    role: "assistant",
                    content: `Error: ${errorMessage}`,
                    sources: sources,
                  };
                } else {
                  updatedMessages[updatedMessages.length - 1] = {
                    ...updatedMessages[updatedMessages.length - 1],
                    role: "assistant",
                    content: cleanedMessage,
                    sources: sources,
                  };
                }
              }
              return updatedMessages;
            });
          }
        } catch (error) {
          console.error("Error reading stream:", error);
        } finally {
          reader.releaseLock();
          try {
            // Use the plain text response directly
            if (!chatId) {
              try {
                const title = await workspaceChatService.generateTitle(
                  input,
                  tenantId
                );
                // Create a new chat with a default title
                const newChat = await createChat({
                  userId,
                  tenantId,
                  title: title?.title?.replaceAll('"', "") ?? "New Chat", // Use a default title since we're not parsing JSON
                });

                const newChatId = newChat.chat.id;
                setChatId(newChatId);

                // Save user message after chat is created with image attachments
                await createMessage({
                  chatId: newChatId,
                  userId,
                  content: input,
                  role: "user",
                  metadata: {
                    includeWebResults: includeWebResults === true,
                    hasImages: selectedImages.length > 0,
                    images:
                      selectedImages.length > 0 ? selectedImages : undefined,
                  },
                });

                // Save the complete assistant message
                const assistantMessageResponse = await createMessage({
                  chatId: newChatId,
                  userId,
                  content: assistantMessage, // Use the raw text response
                  role: "assistant",
                  metadata: {},
                  sources: sources, // Include sources in the saved message
                });

                // Update the assistant message in the UI with its ID
                if (
                  assistantMessageResponse &&
                  !assistantMessageResponse.error
                ) {
                  setMessages((prevMessages) => {
                    const updatedMessages = [...prevMessages];
                    // Update the last message (assistant) with the ID
                    if (updatedMessages.length > 0) {
                      updatedMessages[updatedMessages.length - 1] = {
                        ...updatedMessages[updatedMessages.length - 1],
                        id: assistantMessageResponse.id,
                      };
                    }
                    return updatedMessages;
                  });
                }
                router.push(`/ask-ai/${newChatId}`);
              } catch (error) {
                console.error("Error creating chat:", error);
              }
            } else {
              // Save the complete assistant message
              const assistantMessageResponse = await createMessage({
                chatId: chatId,
                userId,
                content: assistantMessage,
                role: "assistant",
                metadata: {},
                sources: sources, // Include sources in the saved message
              });

              // Update the assistant message in the UI with its ID
              if (assistantMessageResponse && !assistantMessageResponse.error) {
                setMessages((prevMessages) => {
                  const updatedMessages = [...prevMessages];
                  // Update the last message (assistant) with the ID
                  if (updatedMessages.length > 0) {
                    updatedMessages[updatedMessages.length - 1] = {
                      ...updatedMessages[updatedMessages.length - 1],
                      id: assistantMessageResponse.id,
                    };
                  }
                  return updatedMessages;
                });
              }
            }

            // The UI is already updated during streaming, no need to update again
          } catch (parseError) {
            console.error("Error parsing assistant message:", parseError);
          }
        }
      }
    },
    onError: (error) => {
      console.error("Error in chat:", error);
      // Show error message to user
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: "assistant_tempid",
          role: "assistant",
          content: t("chat.errorProcessingRequest"),
        },
      ]);
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);
    setInput(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // Only submit if there's content or images
      if (input.trim() === "" && selectedImages.length === 0) return;

      handleAISubmit(e as unknown as React.FormEvent<HTMLFormElement>);
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: "user_tempid",
          role: "user",
          content: input,
          images: selectedImages.length > 0 ? selectedImages : undefined,
          metadata: {
            includeWebResults: includeWebResults === true,
            hasImages: selectedImages.length > 0,
            images: selectedImages.length > 0 ? selectedImages : undefined,
          },
        },
      ]);
      setInput("");
      setSelectedImages([]);
    }
  };

  // Function to handle web search toggle
  const handleWebSearchToggle = async (include: boolean) => {
    if (include) {
      try {
        // Check if the tenant has exceeded their daily web search limit
        const result = await performWebSearch("test query", tenantId, userId);

        if (result.limitExceeded) {
          setWebSearchLimitExceeded(true);
          toast.error(result.limitInfo?.message || t("chat.tryAgainTomorrow"));
          return;
        }

        setWebSearchLimitExceeded(false);
        setIncludeWebResults(true);
      } catch (error) {
        console.error("Error checking web search limit:", error);
        toast.error(t("chat.webSearchErrorDescription"));
        return;
      }
    } else {
      setIncludeWebResults(false);
    }
  };

  const header = (
    <header className="m-auto flex max-w-96 flex-col gap-3 sm:gap-5 text-center">
      <h1 className="text-xl sm:text-2xl font-semibold leading-none tracking-tight">
        {t("chat.askAi")}
      </h1>
    </header>
  );

  // Render the message list
  const renderMessages = () => {
    return processedMessages.map((message, index) => (
      <ChatMessage
        key={index}
        message={message}
        index={index}
        processedMessages={processedMessages}
        displayIndicesRef={displayIndicesRef}
        isLoading={isLoading}
        updateDisplayIndex={updateDisplayIndex}
        handleFeedback={handleFeedback}
        handleRegenerate={handleRegenerate}
        toggleCitationAccordion={toggleCitationAccordion}
        setSelectedSource={setSelectedSource}
        setIsCitationModalOpen={setIsCitationModalOpen}
      />
    ));
  };

  return (
    <main className="flex h-full flex-col w-full items-center border-none pt-0 relative">
      {/* Chat Header */}
      <ChatHeader
        chatId={chatId}
        chatTitle={chatTitle}
        hasMessages={processedMessages.length > 0}
        messageCount={processedMessages.length}
        lastActivity={
          processedMessages.length > 0
            ? processedMessages[processedMessages.length - 1]?.createdAt
            : undefined
        }
        onTitleUpdate={handleTitleUpdate}
        onChatDelete={handleChatDelete}
      />

      <div
        className={`flex-1 mx-auto my-4 w-full max-w-[50rem] content-center px-3 sm:px-6 ${
          processedMessages.length
            ? "pt-4 sm:pt-8"
            : "pt-4 sm:pt-8 h-full justify-center"
        } pb-24 h-full`}
      >
        {processedMessages.length ? (
          <div className="my-2 sm:my-4 p-2 sm:p-4 flex h-fit min-h-full flex-col gap-3 sm:gap-4 overflow-y-auto">
            {renderMessages()}
            {(status === "streaming" || status === "submitted") && (
              <MessageLoader />
            )}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          header
        )}
      </div>

      {/* Citation Modal */}
      <CitationModal
        isOpen={isCitationModalOpen}
        onClose={() => setIsCitationModalOpen(false)}
        source={selectedSource}
        highlightedText={
          selectedSource?.metadata?.relevantText ||
          selectedSource?.content?.substring(0, 150)
        } // Use relevant text if available, otherwise use a portion of the content
      />

      <div className="absolute bottom-0 left-14 right-0 z-10 flex justify-center">
        <div className="w-full mx-auto max-w-[50rem]">
          <ChatInputForm
            input={input}
            setInput={setInput}
            handleKeyDown={handleKeyDown}
            handleChange={handleChange}
            handleAISubmit={handleAISubmit}
            setMessages={setMessages}
            status={status}
            state={state}
            isMobile={isMobile}
            includeWebResults={includeWebResults}
            setIncludeWebResults={handleWebSearchToggle}
            webSearchLimitExceeded={webSearchLimitExceeded}
            selectedImages={selectedImages}
            setSelectedImages={setSelectedImages}
          />
        </div>
      </div>
    </main>
  );
}
