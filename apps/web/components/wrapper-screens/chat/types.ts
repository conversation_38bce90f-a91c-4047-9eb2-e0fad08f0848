export interface Source {
  content: string;
  metadata: {
    workspace_id?: string;
    fileId?: string;
    page?: number;
    fileName?: string;
    workspace?: {
      slug?: string;
      name?: string;
    };
    relevanceScore?: number;
    relevantText?: string;
    [key: string]: any;
  };
}

export interface ImageAttachment {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
  preview?: string; // Base64 preview for display
}

export interface FileAttachment {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
  preview?: string; // Base64 preview for display
  fileType: 'image' | 'audio' | 'document' | 'text' | 'spreadsheet';
  processingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  processedContent?: string; // Transcribed text, extracted text, etc.
  metadata?: {
    duration?: number; // For audio files
    pages?: number; // For PDFs
    sheets?: string[]; // For Excel files
    dimensions?: { width: number; height: number }; // For images
  };
}

export interface Message {
  id?: string;
  role: "user" | "assistant";
  content: string;
  alternatives?: string[]; // Legacy field - Array of alternative (regenerated) responses
  currentAlternativeIndex?: number; // Legacy field - Index of the currently displayed alternative (-1 for original)
  sources?: Source[]; // Document sources/citations
  originalMessageId?: string; // If this is a regenerated message, points to the original message
  regeneratedMessages?: Message[]; // Messages that are regenerations of this one
  images?: ImageAttachment[]; // Image attachments (legacy)
  files?: FileAttachment[]; // File attachments (new)
  metadata?: {
    feedback?: "like" | "dislike";
    images?: ImageAttachment[]; // Image attachments (legacy)
    files?: FileAttachment[]; // File attachments (new)
    originalResponse?: boolean; // Indicates this is the original response
    regeneratedResponse?: boolean; // Indicates this is a regenerated response
    hidden?: boolean; // To hide messages in the UI
    isSelected?: boolean; // Indicates if this regenerated message is currently selected
    activeRegeneratedIndex?: number | null; // Index of the currently active regenerated message
    generationLevel?: number; // Level of regeneration (1 = first regeneration, 2 = regeneration of regeneration, etc.)
    isStreaming?: boolean; // Indicates if this message is currently streaming content
    includeWebResults?: boolean; // Indicates if web search should be included for this message
    hasImages?: boolean; // Indicates if this message contains images
  };
  createdAt?: string; // For sorting regenerated messages
}

export interface DisplayIndices {
  [messageId: string]: number;
}

export interface RegeneratedMessageCounts {
  [messageId: string]: number;
}

export interface ChatFormProps {
  userId: string;
  chats: {
    id: string;
    title?: string;
    messages: Message[];
  } | null;
  tenantId: string;
  userName: string;
}
