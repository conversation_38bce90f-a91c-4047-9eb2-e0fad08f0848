"use client";

import React, { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { 
  Upload, 
  Plus, 
  FileText, 
  FileAudio, 
  FileImage, 
  FileSpreadsheet,
  File as FileIcon,
  X,
  Loader2
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { toast } from "react-hot-toast";
import { FileAttachment } from "../types";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ChatSupportedFileTypes, 
  getFileTypeFromMimeType,
  MimeTypeMap 
} from "@/lib/constant/supported-extensions";

interface FileUploadProps {
  onFilesSelected: (files: FileAttachment[] | ((prev: FileAttachment[]) => FileAttachment[])) => void;
  selectedFiles: FileAttachment[];
  onRemoveFile: (fileId: string) => void;
  disabled?: boolean;
  maxFiles?: number;
  compact?: boolean; // New prop for compact display
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  selectedFiles,
  onRemoveFile,
  disabled = false,
  maxFiles = 5,
  compact = false,
}) => {
  const { t } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Create accept string for all supported file types
  const supportedMimeTypes = Object.keys(MimeTypeMap);
  const supportedExtensions = Object.values(ChatSupportedFileTypes).flat().map(ext => `.${ext}`);
  const acceptString = [...supportedMimeTypes, ...supportedExtensions].join(",");

  const validateFile = (file: File): boolean => {
    const fileTypeCategory = getFileTypeFromMimeType(file.type);
    
    if (!fileTypeCategory) {
      toast.error(
        t("chat.invalidFileFormat") ||
          "Invalid file format. Please use supported image, audio, document, text, or spreadsheet files."
      );
      return false;
    }

    // File size limits by category
    const sizeLimits = {
      image: 10 * 1024 * 1024, // 10MB
      audio: 50 * 1024 * 1024, // 50MB
      document: 25 * 1024 * 1024, // 25MB
      text: 5 * 1024 * 1024, // 5MB
      spreadsheet: 15 * 1024 * 1024, // 15MB
    };

    const maxSize = sizeLimits[fileTypeCategory];
    if (file.size > maxSize) {
      toast.error(
        t("chat.fileTooLarge") || 
          `File size should be less than ${Math.round(maxSize / (1024 * 1024))}MB for ${fileTypeCategory} files.`
      );
      return false;
    }

    return true;
  };

  const processFiles = async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(validateFile);

    if (selectedFiles.length + validFiles.length > maxFiles) {
      toast.error(
        t("chat.tooManyFiles") || `Maximum ${maxFiles} files allowed.`
      );
      return;
    }

    const newFiles: FileAttachment[] = [];

    for (const file of validFiles) {
      try {
        const fileTypeCategory = getFileTypeFromMimeType(file.type);
        if (!fileTypeCategory) continue;

        // Create preview URL for immediate display (for images)
        const preview = fileTypeCategory === 'image' ? URL.createObjectURL(file) : undefined;

        // Show loading toast
        const loadingToast = toast.loading(`Uploading ${file.name}...`);

        // Upload to server
        const uploadedFile = await uploadFileToServer(file);

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        if (uploadedFile) {
          const fileWithPreview = {
            ...uploadedFile,
            preview,
          };

          newFiles.push(fileWithPreview);
          toast.success(`${file.name} uploaded successfully!`);

          // Trigger background processing for certain file types
          if (['audio', 'document', 'spreadsheet', 'text'].includes(fileTypeCategory)) {
            triggerFileProcessing(fileWithPreview);
          }
        } else {
          toast.error(`Failed to upload ${file.name}`);
        }
      } catch (error) {
        console.error("Error processing file:", error);
        toast.error(`Failed to upload ${file.name}`);
      }
    }

    if (newFiles.length > 0) {
      onFilesSelected([...selectedFiles, ...newFiles]);
    }
  };

  const uploadFileToServer = async (
    file: File
  ): Promise<FileAttachment | null> => {
    try {
      // Get tenant ID from cookies or context
      const tenantId = document.cookie
        .split("; ")
        .find((row) => row.startsWith("currentOrganizationId="))
        ?.split("=")[1];

      if (!tenantId) {
        throw new Error("Tenant ID not found");
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);

      const response = await fetch("/api/chat/upload-file", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading file:", error);
      return null;
    }
  };

  const triggerFileProcessing = async (file: FileAttachment) => {
    try {
      // Update file status to processing using callback to get latest state
      onFilesSelected(currentFiles =>
        currentFiles.map(f =>
          f.id === file.id ? { ...f, processingStatus: 'processing' as const } : f
        )
      );

      const response = await fetch("/api/chat/process-file", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fileId: file.id,
          fileUrl: file.url,
          fileType: file.fileType,
          fileName: file.name,
        }),
      });

      if (response.ok) {
        const result = await response.json();

        // Update file with processed content using callback
        onFilesSelected(currentFiles =>
          currentFiles.map(f =>
            f.id === file.id ? {
              ...f,
              processingStatus: result.processingStatus,
              processedContent: result.processedContent,
              metadata: { ...f.metadata, ...result.metadata }
            } : f
          )
        );

        if (result.processingStatus === 'completed') {
          toast.success(`${file.name} processed successfully!`);
        } else {
          toast.error(`Failed to process ${file.name}: ${result.error || 'Unknown error'}`);
        }
      } else {
        const errorData = await response.json();
        console.error("Processing API error:", errorData);

        // Update file status to failed using callback
        onFilesSelected(currentFiles =>
          currentFiles.map(f =>
            f.id === file.id ? { ...f, processingStatus: 'failed' as const } : f
          )
        );
        toast.error(`Failed to process ${file.name}: ${errorData.error || 'Server error'}`);
      }
    } catch (error) {
      console.error("Error processing file:", error);

      // Update file status to failed using callback
      onFilesSelected(currentFiles =>
        currentFiles.map(f =>
          f.id === file.id ? { ...f, processingStatus: 'failed' as const } : f
        )
      );
      toast.error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const handleRemoveFile = (fileId: string) => {
    // Clean up object URL to prevent memory leaks
    const file = selectedFiles.find((f) => f.id === fileId);
    if (file?.preview) {
      URL.revokeObjectURL(file.preview);
    }
    onRemoveFile(fileId);
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <FileImage className="h-4 w-4" />;
      case 'audio':
        return <FileAudio className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'spreadsheet':
        return <FileSpreadsheet className="h-4 w-4" />;
      default:
        return <FileIcon className="h-4 w-4" />;
    }
  };

  return (
    <div className="">
      {/* Selected Files Display - only show when not in compact mode */}

      {/* Upload Button and Drag Area */}
      <div
        className={cn(
          "relative transition-all duration-200 ease-in-out",
          isDragOver && "scale-105"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptString}
          multiple
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />

        {/* Plus icon with dropdown */}
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className={cn(
                compact
                  ? "h-8 w-8 rounded-full flex items-center justify-center bg-muted text-foreground border border-border hover:bg-accent/80 transition-all duration-200"
                  : "h-9 w-9 rounded-full flex items-center justify-center bg-muted text-foreground border border-border hover:bg-accent/80 transition-all duration-200 mb-2",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={disabled || selectedFiles.length >= maxFiles}
              aria-label={t("chat.uploadFile") || "Add files"}
            >
              <Plus className={compact ? "h-4 w-4" : "h-5 w-5"} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="min-w-[200px]">
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
                setDropdownOpen(false);
                fileInputRef.current?.click();
              }}
              disabled={disabled || selectedFiles.length >= maxFiles}
              className="cursor-pointer"
            >
              <Upload className="h-4 w-4 mr-2" />
              {t("chat.addPhotosAndFiles") || "Add photos and files"}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Enhanced Drag overlay */}
        {isDragOver && (
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary/5 border-2 border-dashed border-primary/60 rounded-xl flex items-center justify-center z-10 backdrop-blur-sm">
            <div className="text-center animate-pulse">
              <div className="bg-primary/10 rounded-full p-3 mx-auto mb-2 w-fit">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <p className="text-sm text-primary font-medium">
                {t("chat.dropFilesHere") || "Drop files here"}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
