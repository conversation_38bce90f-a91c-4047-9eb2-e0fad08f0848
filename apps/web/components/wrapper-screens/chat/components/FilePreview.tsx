"use client";

import React, { useState } from "react";
import Image from "next/image";
import { FileAttachment } from "../types";
import { cn } from "@/lib/utils";
import { 
  FileText, 
  FileAudio, 
  FileImage, 
  FileSpreadsheet,
  File as FileIcon,
  Play,
  Pause,
  Download,
  Eye,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface FilePreviewProps {
  files: FileAttachment[];
  className?: string;
}

interface SingleFilePreviewProps {
  file: FileAttachment;
  isInGrid?: boolean;
}

const SingleFilePreview: React.FC<SingleFilePreviewProps> = ({ file, isInGrid = false }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showFullContent, setShowFullContent] = useState(false);

  const getFileIcon = (fileType: string, size: string = "h-6 w-6") => {
    const iconClass = `${size} text-muted-foreground`;
    switch (fileType) {
      case 'image':
        return <FileImage className={iconClass} />;
      case 'audio':
        return <FileAudio className={iconClass} />;
      case 'document':
        return <FileText className={iconClass} />;
      case 'text':
        return <FileText className={iconClass} />;
      case 'spreadsheet':
        return <FileSpreadsheet className={iconClass} />;
      default:
        return <FileIcon className={iconClass} />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderFileContent = () => {
    switch (file.fileType) {
      case 'image':
        return (
          <div className="relative group">
            <Image
              src={file.preview || file.url}
              alt={file.name}
              width={isInGrid ? 150 : 300}
              height={isInGrid ? 150 : 200}
              className="rounded-lg object-cover border border-border/50"
              onError={(e) => {
                // Fallback to file icon if image fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="secondary" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl">
                  <DialogHeader>
                    <DialogTitle>{file.name}</DialogTitle>
                  </DialogHeader>
                  <div className="flex justify-center">
                    <Image
                      src={file.preview || file.url}
                      alt={file.name}
                      width={800}
                      height={600}
                      className="max-w-full h-auto rounded-lg"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        );

      case 'audio':
        return (
          <div className="bg-muted/30 rounded-lg p-4 border border-border/50">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 rounded-full p-2">
                <FileAudio className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{file.name}</p>
                <p className="text-xs text-muted-foreground">
                  {formatFileSize(file.size)}
                  {file.metadata?.duration && (
                    <span className="ml-2">• {Math.round(file.metadata.duration)}s</span>
                  )}
                </p>
              </div>
              <audio controls className="max-w-xs">
                <source src={file.url} type={file.type} />
                Your browser does not support the audio element.
              </audio>
            </div>
            {file.processedContent && (
              <div className="mt-3 pt-3 border-t border-border/50">
                <p className="text-xs text-muted-foreground mb-2">Transcription:</p>
                <p className="text-sm bg-background/50 rounded p-2 max-h-20 overflow-y-auto">
                  {file.processedContent}
                </p>
              </div>
            )}
          </div>
        );

      case 'document':
      case 'text':
      case 'spreadsheet':
        return (
          <div className="bg-muted/30 rounded-lg p-4 border border-border/50">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 rounded-full p-2">
                {getFileIcon(file.fileType, "h-6 w-6")}
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{file.name}</p>
                <p className="text-xs text-muted-foreground">
                  {formatFileSize(file.size)}
                  {file.metadata?.pages && (
                    <span className="ml-2">• {file.metadata.pages} pages</span>
                  )}
                  {file.metadata?.sheets && (
                    <span className="ml-2">• {file.metadata.sheets.length} sheets</span>
                  )}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" asChild>
                  <a href={file.url} target="_blank" rel="noopener noreferrer">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href={file.url} download={file.name}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </a>
                </Button>
              </div>
            </div>
            
            {file.processedContent && (
              <div className="mt-3 pt-3 border-t border-border/50">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-xs text-muted-foreground">Content Preview:</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFullContent(!showFullContent)}
                    className="text-xs"
                  >
                    {showFullContent ? 'Show Less' : 'Show More'}
                  </Button>
                </div>
                <div className={cn(
                  "text-sm bg-background/50 rounded p-2 overflow-y-auto",
                  showFullContent ? "max-h-60" : "max-h-20"
                )}>
                  {file.fileType === 'spreadsheet' ? (
                    <pre className="whitespace-pre-wrap font-mono text-xs">
                      {file.processedContent}
                    </pre>
                  ) : (
                    <p className="whitespace-pre-wrap">
                      {showFullContent 
                        ? file.processedContent 
                        : file.processedContent.substring(0, 200) + (file.processedContent.length > 200 ? '...' : '')
                      }
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="bg-muted/30 rounded-lg p-4 border border-border/50">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 rounded-full p-2">
                <FileIcon className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{file.name}</p>
                <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
              </div>
              <Button variant="outline" size="sm" asChild>
                <a href={file.url} download={file.name}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </a>
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="relative">
      {file.processingStatus === 'processing' && (
        <div className="absolute top-2 right-2 z-10 bg-background/80 backdrop-blur-sm rounded-full p-1">
          <Loader2 className="h-4 w-4 animate-spin text-primary" />
        </div>
      )}
      {renderFileContent()}
    </div>
  );
};

export const FilePreview: React.FC<FilePreviewProps> = ({ files, className }) => {
  if (!files || files.length === 0) return null;

  return (
    <div className={cn("space-y-3", className)}>
      {files.length === 1 ? (
        <SingleFilePreview file={files[0]} />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {files.map((file) => (
            <SingleFilePreview key={file.id} file={file} isInGrid={true} />
          ))}
        </div>
      )}
    </div>
  );
};
