import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpIcon, Globe, FileText, FileAudio, FileImage, FileSpreadsheet, File as FileIcon, X } from "lucide-react";
import { AutoResizeTextarea } from "@/components/ui/autoresize-textarea";
import { useLanguage } from "@/lib/language-context";
import { Message, ImageAttachment, FileAttachment } from "../types";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageUpload } from "./ImageUpload";
import { FileUpload } from "./FileUpload";
import { cn } from "@/lib/utils";

interface ChatInputFormProps {
  input: string;
  setInput: (input: string) => void;
  handleKeyDown: (e: any) => void;
  handleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleAISubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  status: string;
  state: string;
  isMobile: boolean;
  includeWebResults: boolean;
  setIncludeWebResults: (include: boolean) => void;
  webSearchLimitExceeded?: boolean;
  selectedImages: ImageAttachment[];
  setSelectedImages: (images: ImageAttachment[]) => void;
  selectedFiles?: FileAttachment[];
  setSelectedFiles?: (files: FileAttachment[]) => void;
  useNewFileUpload?: boolean; // Flag to enable new file upload system
}

export const ChatInputForm: React.FC<ChatInputFormProps> = ({
  input,
  setInput,
  handleKeyDown,
  handleChange,
  handleAISubmit,
  setMessages,
  status,
  includeWebResults,
  setIncludeWebResults,
  webSearchLimitExceeded,
  selectedImages,
  setSelectedImages,
  selectedFiles = [],
  setSelectedFiles,
  useNewFileUpload = false,
}) => {
  const { t } = useLanguage();

  const handleImagesSelected = (images: ImageAttachment[]) => {
    setSelectedImages(images);
  };

  const handleRemoveImage = (imageId: string) => {
    setSelectedImages(selectedImages.filter((img) => img.id !== imageId));
  };

  const handleFilesSelected = (files: FileAttachment[]) => {
    if (setSelectedFiles) {
      setSelectedFiles(files);
    }
  };

  const handleRemoveFile = (fileId: string) => {
    if (setSelectedFiles) {
      setSelectedFiles(selectedFiles.filter((file) => file.id !== fileId));
    }
  };

  // Convert FileAttachment to ImageAttachment for backward compatibility
  const convertFilesToImages = (files: FileAttachment[]): ImageAttachment[] => {
    return files
      .filter(file => file.fileType === 'image')
      .map(file => ({
        id: file.id,
        url: file.url,
        name: file.name,
        type: file.type,
        size: file.size,
        preview: file.preview,
      }));
  };

  const canSubmit = input.trim() !== "" || selectedImages.length > 0 || selectedFiles.length > 0;

  return (
    <div className="flex flex-col items-center w-full">
      {/* Web search toggle */}
      <div className="flex items-center w-full mb-2">
        <div className="px-3 py-1 bg-background/80 backdrop-blur-sm rounded-full border border-input shadow-sm transition-all duration-200 ease-linear">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <Switch
                    id="web-search"
                    checked={includeWebResults === true}
                    onCheckedChange={(checked) => {
                      console.log("Web search toggle changed to:", checked);
                      setIncludeWebResults(checked);
                    }}
                    disabled={
                      webSearchLimitExceeded ||
                      status === "streaming" ||
                      status === "submitted"
                    }
                    className={
                      webSearchLimitExceeded
                        ? "cursor-not-allowed opacity-50"
                        : ""
                    }
                  />
                  <Label
                    htmlFor="web-search"
                    className="flex items-center gap-1 text-xs cursor-pointer"
                  >
                    <Globe className="h-3 w-3" />
                    {t("chat.includeWebResults")}
                  </Label>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                {webSearchLimitExceeded
                  ? t("chat.webSearchLimitExceeded")
                  : t("chat.webSearchTooltip")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Enhanced Image Upload Section */}
      {/* {selectedImages.length > 0 && (
        <div className="w-full max-w-[45rem] mb-4">
          <div className="bg-muted/30 rounded-xl p-4 border border-border/50">
            <div className="flex items-center gap-2 mb-3">
              <div className="bg-primary/10 rounded-full p-1">
                <ImageIcon className="h-4 w-4 text-primary" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("chat.attachedImages") || "Attached Images"}
              </span>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
                {selectedImages.length}
              </span>
            </div>
            <ImageUpload
              onImagesSelected={handleImagesSelected}
              selectedImages={selectedImages}
              onRemoveImage={handleRemoveImage}
              disabled={status === "streaming" || status === "submitted"}
            />
          </div>
        </div>
      )} */}

      {/* File Upload Section - Above Input */}
      {useNewFileUpload && (
        <div className="w-full max-w-[45rem] mb-3">
          <div className="flex items-center gap-2">
            {/* File Upload Button */}
            <FileUpload
              onFilesSelected={handleFilesSelected}
              selectedFiles={selectedFiles}
              onRemoveFile={handleRemoveFile}
              disabled={status === "streaming" || status === "submitted"}
              compact={true}
            />

            {/* File Chips Display */}
            {selectedFiles.length > 0 && (
              <div className="flex flex-wrap gap-2 flex-1">
                {selectedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="inline-flex items-center gap-2 px-3 py-1.5 bg-muted/80 rounded-full border border-border/50 text-sm max-w-[200px]"
                  >
                    <div className="flex items-center gap-1.5 min-w-0">
                      {file.fileType === 'document' && <FileText className="h-3 w-3 text-red-500 flex-shrink-0" />}
                      {file.fileType === 'image' && <FileImage className="h-3 w-3 text-blue-500 flex-shrink-0" />}
                      {file.fileType === 'audio' && <FileAudio className="h-3 w-3 text-green-500 flex-shrink-0" />}
                      {file.fileType === 'spreadsheet' && <FileSpreadsheet className="h-3 w-3 text-orange-500 flex-shrink-0" />}
                      {!['document', 'image', 'audio', 'spreadsheet'].includes(file.fileType) && <FileIcon className="h-3 w-3 text-gray-500 flex-shrink-0" />}
                      <span className="truncate text-xs font-medium">
                        {file.name}
                      </span>
                      <span className="text-xs text-muted-foreground flex-shrink-0">
                        {(file.size / 1024 / 1024).toFixed(1)} MB
                      </span>
                    </div>
                    <button
                      onClick={() => handleRemoveFile(file.id)}
                      disabled={status === "streaming" || status === "submitted"}
                      className="flex-shrink-0 hover:bg-destructive/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Chat input form */}
      <div className="w-full flex items-center">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (!canSubmit) return;

            // Prepare message with both legacy images and new files
            const messageImages = useNewFileUpload
              ? convertFilesToImages(selectedFiles)
              : selectedImages;

            setMessages((prevMessages) => [
              ...prevMessages,
              {
                id: "user_tempid",
                role: "user",
                content: input,
                images: messageImages.length > 0 ? messageImages : undefined,
                files: useNewFileUpload && selectedFiles.length > 0 ? selectedFiles : undefined,
                metadata: {
                  includeWebResults: includeWebResults === true,
                  hasImages: messageImages.length > 0,
                  files: useNewFileUpload && selectedFiles.length > 0 ? selectedFiles : undefined,
                },
              },
            ]);
            handleAISubmit(e);
            // Clear files after submission
            if (useNewFileUpload && setSelectedFiles) {
              setSelectedFiles([]);
            }
          }}
          className={cn(
            "w-[calc(100%-2rem)] md:w-full max-w-[45rem] bg-background border border-input rounded-[16px] px-3 sm:px-4 py-2 sm:py-3 flex items-center shadow-lg transition-all duration-200 ease-linear mb-6",
            (selectedImages.length > 0 || selectedFiles.length > 0) && "border-primary/30 shadow-primary/10"
          )}
        >
          {/* Legacy Image upload button - only show when not using new file upload */}
          {!useNewFileUpload && (
            <div className="mr-2">
              <ImageUpload
                onImagesSelected={handleImagesSelected}
                selectedImages={selectedImages}
                onRemoveImage={handleRemoveImage}
                disabled={status === "streaming" || status === "submitted"}
              />
            </div>
          )}

          <AutoResizeTextarea
            onKeyDown={handleKeyDown}
            onChange={(e: any) => handleChange(e)}
            value={input}
            placeholder={
              useNewFileUpload
                ? selectedFiles.length > 0
                  ? t("chat.enterMessageWithFiles") ||
                    "Ask about your files or add a message..."
                  : t("chat.enterMessage")
                : selectedImages.length > 0
                ? t("chat.enterMessageWithImages") ||
                  "Ask about your images or add a message..."
                : t("chat.enterMessage")
            }
            className="flex-1 bg-transparent text-sm sm:text-base focus:outline-none px-1 py-1 sm:px-2"
            disabled={status === "streaming" || status === "submitted"}
          />
          <Button
            size="icon"
            className="rounded-full h-8 w-8 sm:h-10 sm:w-10"
            disabled={
              !canSubmit || status === "streaming" || status === "submitted"
            }
          >
            <ArrowUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
};
