This is a test text file for the file upload system.

It contains multiple lines of text to test the text processing functionality.

Features being tested:
- File upload to Azure Blob Storage
- Text file processing
- File content extraction
- Chat integration

This file should be processed successfully and its content should be extracted and displayed in the chat interface.

The system supports various file formats:
- Images (JPG, PNG, WebP, GIF, BMP)
- Audio files (MP3, WAV, M4A, OGG, FLAC)
- Documents (PDF, DOCX, PPTX)
- Text files (TXT, MD)
- Spreadsheets (CSV, XLSX, XLS)

Each file type has specific processing:
- Images: Vision model analysis
- Audio: Transcription (placeholder)
- PDFs: Text extraction with OCR fallback
- Text files: Direct content reading
- Spreadsheets: Parsing and table formatting

This test file should demonstrate the text file processing capability.
